/**
 * Professional Homepage Styles - Arabic RTL E-commerce
 * Modern, Clean, and Error-Free Implementation
 * Optimized for Performance and Accessibility
 *
 * @version 4.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   CSS Custom Properties (Variables) for Consistency
   ========================================================================== */

:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #764ba2;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Secondary Colors */
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --accent-color: #e74c3c;

    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #5a6c7d;
    --text-muted: #6c757d;
    --text-light: #ffffff;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-light: #e9ecef;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.4s ease;

    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
}

/* ==========================================================================
   Base RTL Layout & Typography
   ========================================================================== */

body {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    scroll-behavior: smooth;

    /* Enhanced Arabic Text Rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "kern" 1, "liga" 1;

    /* Arabic-specific optimizations */
    word-spacing: 0.1em;
    letter-spacing: 0.01em;
}

/* ==========================================================================
   Enhanced Arabic Typography & RTL Support
   ========================================================================== */

/* Arabic Font Loading with Fallbacks */
@font-face {
    font-family: 'Cairo-Optimized';
    src: local('Cairo'), local('Segoe UI'), local('Tahoma');
    font-display: swap;
}

/* Arabic Text Optimization */
.arabic-text {
    font-family: 'Cairo', 'Amiri', 'Scheherazade', serif;
    font-weight: 400;
    line-height: 1.8;
    text-align: justify;
    word-spacing: 0.1em;
}

.arabic-title {
    font-family: 'Cairo', 'Amiri', serif;
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.arabic-subtitle {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    line-height: 1.6;
    color: var(--text-muted);
}

/* Iraqi Dinar Currency Formatting */
.currency {
    font-family: 'Cairo', monospace;
    font-weight: 600;
    direction: ltr;
    display: inline-block;
    text-align: left;
}

.currency::after {
    content: ' د.ع';
    font-family: 'Cairo', sans-serif;
    margin-right: 0.25rem;
    direction: rtl;
}

/* Enhanced RTL Layout Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

[dir="rtl"] .float-left {
    float: right !important;
}

[dir="rtl"] .float-right {
    float: left !important;
}

/* RTL Margin and Padding Utilities */
[dir="rtl"] .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
[dir="rtl"] .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
[dir="rtl"] .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
[dir="rtl"] .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
[dir="rtl"] .me-5 { margin-left: 3rem !important; margin-right: 0 !important; }

[dir="rtl"] .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
[dir="rtl"] .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
[dir="rtl"] .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }
[dir="rtl"] .ms-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
[dir="rtl"] .ms-5 { margin-right: 3rem !important; margin-left: 0 !important; }

/* RTL Flexbox Utilities */
[dir="rtl"] .justify-content-start {
    justify-content: flex-end !important;
}

[dir="rtl"] .justify-content-end {
    justify-content: flex-start !important;
}

/* Arabic Date and Time Display */
.arabic-date {
    direction: rtl;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    color: var(--text-secondary);
}

/* Arabic Number Formatting */
.arabic-number {
    font-family: 'Cairo', monospace;
    font-weight: 600;
    direction: ltr;
    display: inline-block;
}

/* ==========================================================================
   Professional Section Framework
   ========================================================================== */

.homepage-section {
    position: relative;
    padding: 4rem 0;
    margin: 0;
    overflow: hidden;
}

.homepage-section:not(:last-child) {
    margin-bottom: 0;
}

/* Section Backgrounds */
.homepage-section.bg-white {
    background-color: #ffffff;
}

.homepage-section.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.homepage-section.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.homepage-section.bg-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

/* ==========================================================================
   Professional Section Dividers
   ========================================================================== */

.section-divider {
    position: relative;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e9ecef 20%, #dee2e6 50%, #e9ecef 80%, transparent 100%);
    margin: 0;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-divider.divider-gradient {
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 20%, rgba(118, 75, 162, 0.5) 50%, rgba(102, 126, 234, 0.3) 80%, transparent 100%);
    height: 2px;
}

.section-divider.divider-dots {
    background: none;
    text-align: center;
    height: 20px;
}

.section-divider.divider-dots::before {
    content: '• • •';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #667eea;
    font-size: 1.5rem;
    letter-spacing: 0.5rem;
    width: auto;
    height: auto;
    background: none;
    border-radius: 0;
}

/* ==========================================================================
   Professional Section Headers
   ========================================================================== */

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.homepage-section.bg-gradient .section-title,
.homepage-section.bg-dark .section-title {
    color: white;
}

.homepage-section.bg-gradient .section-subtitle,
.homepage-section.bg-dark .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.homepage-section.bg-gradient .section-title::after,
.homepage-section.bg-dark .section-title::after {
    background: rgba(255, 255, 255, 0.3);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background-color: rgba(0,0,0,0.8);
    transform: translateY(-50%) scale(1.1);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 24px;
    height: 24px;
}

/* Enhanced Product Cards with Professional Visual Effects */
.product-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
    background: white;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
    z-index: 1;
    pointer-events: none;
}

.product-card:hover::before {
    left: 100%;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.product-card:hover .quick-actions {
    opacity: 1;
    transform: translateY(0);
}

/* ==========================================================================
   Quick Action Buttons for Product Cards
   ========================================================================== */

.quick-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 2;
}

[dir="rtl"] .quick-actions {
    right: auto;
    left: 15px;
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background: var(--primary-gradient);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.quick-action-btn i {
    font-size: 1rem;
}

/* Product Image Container Enhancement */
.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.product-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-image-container::after {
    opacity: 1;
}

.product-card .card-img-top {
    height: 250px;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
}

.price-section {
    text-align: right;
}

/* ==========================================================================
   Enhanced Influencers Section - Improved Color Contrast
   ========================================================================== */

.influencers-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    position: relative;
}

.influencers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Enhanced Influencer Cards */
.influencer-card-enhanced {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border-radius: 20px;
    overflow: hidden;
    background: #ffffff;
    position: relative;
}

.influencer-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.influencer-card-enhanced:hover::before {
    opacity: 1;
}

.influencer-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

/* Enhanced Header with Better Contrast */
.influencer-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    border-bottom: none;
    padding: 1.5rem;
    position: relative;
}

.influencer-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.influencer-avatar-enhanced {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255,255,255,0.9);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.influencer-card-enhanced:hover .influencer-avatar-enhanced {
    transform: scale(1.1);
    border-color: #ffffff;
}

/* Enhanced Text Contrast */
.influencer-name {
    color: #ffffff !important;
    font-weight: 700;
    font-size: 1.1rem;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    margin-bottom: 0.5rem;
}

.content-type-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff !important;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Enhanced Body Content */
.influencer-body {
    padding: 1.5rem;
    background: #ffffff;
}

.influencer-content-title {
    color: #2c3e50 !important;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.influencer-description {
    color: #5a6c7d !important;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Enhanced Rating Section */
.rating-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    margin-bottom: 1rem;
}

.rating-stars {
    display: flex;
    gap: 0.2rem;
}

.rating-star {
    font-size: 1.1rem;
    color: #f39c12;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.rating-star.bi-star {
    color: #ddd;
}

.rating-text {
    font-weight: 600;
    color: #e67e22;
    font-size: 0.9rem;
}

/* Enhanced Video Button */
.video-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.video-btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
}

/* View All Button Enhancement */
.view-all-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 30px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.view-all-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* ==========================================================================
   Enhanced Success Story Section - Professional Design
   ========================================================================== */

.success-story-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.success-story-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.success-story-title {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    position: relative;
}

.title-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    margin: 1rem auto 2rem;
}

.story-content-wrapper {
    padding: 2rem;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    margin-bottom: 2rem;
}

.story-content-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px 20px 0 0;
}

.story-main-text {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.story-additional-text {
    color: #5a6c7d;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    text-align: justify;
}

.story-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.feature-item i {
    color: #28a745;
    font-size: 1.2rem;
}

.feature-item span {
    color: #155724;
    font-weight: 600;
}

.story-image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.story-image {
    width: 100%;
    height: auto;
    transition: all 0.4s ease;
}

.story-image-wrapper:hover .story-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.story-image-wrapper:hover .image-overlay {
    opacity: 1;
}

/* Enhanced Achievement Cards */
.achievements-section {
    margin-top: 3rem;
    padding: 2rem 0;
}

.achievement-card {
    background: #ffffff;
    padding: 2rem 1.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.achievement-card:hover::before {
    transform: scaleX(1);
}

.achievement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.achievement-card:hover .achievement-icon {
    transform: scale(1.1) rotate(5deg);
}

.achievement-icon i {
    color: #ffffff;
    font-size: 2rem;
}

.achievement-number {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.achievement-label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

/* ==========================================================================
   Brand Story Timeline Section - Professional Timeline Component
   ========================================================================== */

.brand-timeline-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.brand-timeline-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="80" cy="80" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.15"/><circle cx="10" cy="70" r="0.5" fill="%23ffffff" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    pointer-events: none;
}

.timeline-title {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.timeline-subtitle {
    color: #bdc3c7;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.timeline-wrapper {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
}

.timeline-marker {
    position: absolute;
    left: 50%;
    top: 2rem;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    z-index: 2;
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-marker {
    transform: translateX(-50%) scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

.timeline-year {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 800;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.timeline-content {
    background: #ffffff;
    color: #2c3e50;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    position: relative;
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-content {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.timeline-right .timeline-content {
    margin-left: 0;
    margin-right: 6rem;
}

.timeline-left .timeline-content {
    margin-right: 0;
    margin-left: 6rem;
}

.timeline-right .timeline-content::before {
    content: '';
    position: absolute;
    top: 2rem;
    right: -15px;
    width: 0;
    height: 0;
    border-left: 15px solid #ffffff;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.timeline-left .timeline-content::before {
    content: '';
    position: absolute;
    top: 2rem;
    left: -15px;
    width: 0;
    height: 0;
    border-right: 15px solid #ffffff;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.timeline-event-title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.timeline-description {
    color: #5a6c7d;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    text-align: justify;
}

.timeline-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    transform: translateY(-2px);
}

.stat-item i {
    font-size: 1rem;
}

/* Future Vision Section */
.future-vision {
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 800px;
    margin: 0 auto;
}

.vision-title {
    color: #ffffff;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.vision-text {
    color: #ecf0f1;
    font-size: 1.1rem;
    line-height: 1.7;
    margin: 0;
    text-align: center;
}

/* ==========================================================================
   Responsive Design for Enhanced Sections
   ========================================================================== */

/* Tablet Styles */
@media (max-width: 992px) {
    /* Timeline Responsive */
    .timeline-line {
        left: 30px;
    }

    .timeline-marker {
        left: 30px;
        width: 60px;
        height: 60px;
    }

    .timeline-right .timeline-content,
    .timeline-left .timeline-content {
        margin-left: 80px;
        margin-right: 0;
    }

    .timeline-right .timeline-content::before,
    .timeline-left .timeline-content::before {
        left: -15px;
        right: auto;
        border-right: 15px solid #ffffff;
        border-left: none;
    }

    .timeline-title {
        font-size: 2rem;
    }

    .success-story-title {
        font-size: 2rem;
    }

    .story-features {
        margin-top: 1.5rem;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Influencers Section Mobile */
    .influencer-card-enhanced {
        margin-bottom: 1.5rem;
    }

    .influencer-header {
        padding: 1rem;
    }

    .influencer-avatar-enhanced {
        width: 50px;
        height: 50px;
    }

    .influencer-name {
        font-size: 1rem;
    }

    .content-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    /* Success Story Mobile */
    .success-story-title {
        font-size: 1.8rem;
    }

    .story-content-wrapper {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .story-main-text {
        font-size: 1.1rem;
    }

    .story-additional-text {
        font-size: 0.95rem;
    }

    .achievement-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }

    .achievement-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .achievement-icon i {
        font-size: 1.5rem;
    }

    .achievement-number {
        font-size: 2rem;
    }

    /* Timeline Mobile */
    .timeline-title {
        font-size: 1.8rem;
    }

    .timeline-subtitle {
        font-size: 1rem;
    }

    .timeline-marker {
        width: 50px;
        height: 50px;
        left: 25px;
    }

    .timeline-year {
        font-size: 1rem;
    }

    .timeline-content {
        padding: 1.5rem;
        margin-left: 70px;
        margin-right: 0;
    }

    .timeline-event-title {
        font-size: 1.3rem;
    }

    .timeline-description {
        font-size: 0.95rem;
    }

    .timeline-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-item {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .future-vision {
        padding: 1.5rem;
    }

    .vision-title {
        font-size: 1.5rem;
    }

    .vision-text {
        font-size: 1rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 576px) {
    .influencer-header {
        padding: 0.75rem;
    }

    .influencer-body {
        padding: 1rem;
    }

    .story-content-wrapper {
        padding: 1rem;
    }

    .story-features {
        gap: 0.75rem;
    }

    .feature-item {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .achievement-card {
        padding: 1rem;
    }

    .timeline-content {
        padding: 1rem;
        margin-left: 60px;
    }

    .timeline-marker {
        width: 40px;
        height: 40px;
        left: 20px;
    }

    .timeline-year {
        font-size: 0.9rem;
    }

    .future-vision {
        padding: 1rem;
    }
}

/* ==========================================================================
   Arabic RTL Support for Enhanced Sections
   ========================================================================== */

[dir="rtl"] .influencer-avatar-enhanced {
    margin-left: 1rem;
    margin-right: 0;
}

[dir="rtl"] .feature-item:hover {
    transform: translateX(5px);
}

[dir="rtl"] .timeline-right .timeline-content {
    margin-right: 0;
    margin-left: 6rem;
}

[dir="rtl"] .timeline-left .timeline-content {
    margin-left: 0;
    margin-right: 6rem;
}

[dir="rtl"] .timeline-right .timeline-content::before {
    right: auto;
    left: -15px;
    border-right: 15px solid #ffffff;
    border-left: none;
}

[dir="rtl"] .timeline-left .timeline-content::before {
    left: auto;
    right: -15px;
    border-left: 15px solid #ffffff;
    border-right: none;
}

@media (max-width: 992px) {
    [dir="rtl"] .timeline-line {
        right: 30px;
        left: auto;
    }

    [dir="rtl"] .timeline-marker {
        right: 30px;
        left: auto;
    }

    [dir="rtl"] .timeline-right .timeline-content,
    [dir="rtl"] .timeline-left .timeline-content {
        margin-right: 80px;
        margin-left: 0;
    }

    [dir="rtl"] .timeline-right .timeline-content::before,
    [dir="rtl"] .timeline-left .timeline-content::before {
        right: -15px;
        left: auto;
        border-left: 15px solid #ffffff;
        border-right: none;
    }
}

@media (max-width: 768px) {
    [dir="rtl"] .timeline-content {
        margin-right: 70px;
        margin-left: 0;
    }

    [dir="rtl"] .timeline-marker {
        right: 25px;
        left: auto;
    }
}

@media (max-width: 576px) {
    [dir="rtl"] .timeline-content {
        margin-right: 60px;
        margin-left: 0;
    }

    [dir="rtl"] .timeline-marker {
        right: 20px;
        left: auto;
    }
}

/* ==========================================================================
   Enhanced Mobile Optimization & Performance
   ========================================================================== */

/* Mobile-First Responsive Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.img-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    object-position: center;
}

/* Mobile Touch Optimization */
@media (max-width: 768px) {
    .btn {
        min-height: 44px;
        min-width: 44px;
        padding: 0.75rem 1.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .homepage-section {
        padding: 2rem 0;
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-base);
    }
}

/* Small Mobile Optimization */
@media (max-width: 480px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .btn {
        font-size: var(--font-size-sm);
        padding: 0.5rem 1rem;
    }
}

/* Landscape Mobile Optimization */
@media (max-width: 768px) and (orientation: landscape) {
    .homepage-section {
        padding: 1.5rem 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }
}

/* High DPI Display Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .section-title::after {
        height: 2px;
    }

    .section-divider {
        height: 0.5px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #e9ecef;
        --text-muted: #adb5bd;
        --bg-primary: #212529;
        --bg-secondary: #343a40;
        --bg-light: #495057;
    }

    body {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }

    .card {
        background-color: var(--bg-secondary);
        border-color: var(--bg-light);
    }

    .homepage-section.bg-light {
        background: var(--bg-secondary);
    }
}

/* Print Optimization */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    .homepage-section {
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 18pt;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        font-size: 12pt;
    }

    .btn,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    .fade-in-up,
    .fade-in-left,
    .fade-in-right {
        transition: none !important;
        animation: none !important;
    }

    .homepage-section {
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Focus Visible Support */
.btn:focus-visible,
.form-control:focus-visible,
.nav-link:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Hover Media Query for Touch Devices */
@media (hover: hover) {
    .card:hover {
        transform: translateY(-5px);
    }

    .btn:hover {
        transform: translateY(-2px);
    }
}

@media (hover: none) {
    .card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* ==========================================================================
   Performance & Accessibility Optimizations
   ========================================================================== */

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Smooth Scrolling Enhancement */
html {
    scroll-behavior: smooth;
}

/* Focus Styles for Accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .homepage-section {
        border: 2px solid;
    }

    .section-divider {
        background: currentColor;
        height: 3px;
    }

    .card,
    .btn {
        border: 2px solid;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print Styles */
@media print {
    .homepage-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .btn,
    .carousel-control-prev,
    .carousel-control-next {
        display: none !important;
    }

    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}

/* Cart Bounce Animation */
@keyframes cartBounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

.cart-bounce {
    animation: cartBounce 0.6s ease;
}

/* Ripple Effect for Buttons */
.btn-ripple {
    position: relative;
    overflow: hidden;
}

.btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn-ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Review Cards */
.review-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.review-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.rating {
    display: flex;
    gap: 2px;
    justify-content: flex-end;
}

/* Achievement Items */
.achievement-item {
    padding: 1.5rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Newsletter Section */
.newsletter-section {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    margin: 2rem 0;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    z-index: 1;
}

.newsletter-section .container {
    position: relative;
    z-index: 2;
}

/* ==========================================================================
   Professional Special Offers Section - Enhanced Version 4.0
   ========================================================================== */

.special-offers-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.special-offers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(220, 53, 69, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(200, 35, 51, 0.03) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23dc3545" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.special-offers-section::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.08) 0%, rgba(200, 35, 51, 0.08) 100%);
    border-radius: 50%;
    filter: blur(60px);
    animation: floatShape 20s ease-in-out infinite;
}

@keyframes floatShape {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(-30px, -30px) rotate(120deg); }
    66% { transform: translate(30px, -20px) rotate(240deg); }
}

.offers-section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

.offers-section-title {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    position: relative;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.offers-section-title::before {
    content: '🏷️';
    position: absolute;
    top: -20px;
    right: -40px;
    font-size: 2rem;
    opacity: 0.6;
    animation: bounce 2s ease-in-out infinite;
}

[dir="rtl"] .offers-section-title::before {
    right: auto;
    left: -40px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.offers-section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 6px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.offers-section-subtitle {
    font-size: 1.3rem;
    color: #495057;
    font-weight: 500;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    position: relative;
}

.offers-section-subtitle::before,
.offers-section-subtitle::after {
    content: '✨';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    opacity: 0.7;
    animation: sparkle 3s ease-in-out infinite;
}

.offers-section-subtitle::before {
    left: -40px;
}

.offers-section-subtitle::after {
    right: -40px;
    animation-delay: 1.5s;
}

[dir="rtl"] .offers-section-subtitle::before {
    left: auto;
    right: -40px;
}

[dir="rtl"] .offers-section-subtitle::after {
    right: auto;
    left: -40px;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.7; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.2); }
}

/* Enhanced Professional Offers Banner */
.offers-banner {
    position: relative;
    border-radius: 30px;
    overflow: hidden;
    margin-bottom: 4rem;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
    box-shadow:
        0 20px 60px rgba(220, 53, 69, 0.25),
        0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 350px;
    display: flex;
    align-items: center;
}

.offers-banner:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 30px 80px rgba(220, 53, 69, 0.35),
        0 15px 40px rgba(0, 0, 0, 0.15);
}

.offers-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="bannerPattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23bannerPattern)"/></svg>');
    z-index: 1;
}

.offers-banner::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.offers-banner-content {
    position: relative;
    z-index: 2;
    padding: 4rem 3rem;
    color: white;
    text-align: center;
    width: 100%;
}

.offers-banner-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    letter-spacing: -0.02em;
    line-height: 1.2;
    position: relative;
}

.offers-banner-title::before {
    content: '🎯';
    position: absolute;
    top: -15px;
    right: -50px;
    font-size: 2.5rem;
    animation: pulse 2s ease-in-out infinite;
}

[dir="rtl"] .offers-banner-title::before {
    right: auto;
    left: -50px;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 1; }
}

.offers-banner-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 400;
    line-height: 1.5;
}

.offers-banner-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #dc3545;
    padding: 1.2rem 3rem;
    border-radius: 60px;
    font-weight: 700;
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        0 8px 25px rgba(255, 255, 255, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.offers-banner-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
    transition: left 0.6s ease;
}

.offers-banner-btn:hover::before {
    left: 100%;
}

.offers-banner-btn:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #c82333;
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 12px 35px rgba(255, 255, 255, 0.5),
        0 8px 25px rgba(0, 0, 0, 0.15);
}

.offers-banner-btn i {
    transition: transform 0.3s ease;
}

.offers-banner-btn:hover i {
    transform: translateX(-3px);
}

[dir="rtl"] .offers-banner-btn:hover i {
    transform: translateX(3px);
}

/* Professional Enhanced Offer Cards */
.offer-card {
    background: white;
    border-radius: 25px;
    overflow: hidden;
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.08),
        0 4px 15px rgba(220, 53, 69, 0.05);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.05) 100%);
    border-radius: 25px;
    padding: 2px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.offer-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow:
        0 25px 60px rgba(220, 53, 69, 0.2),
        0 15px 35px rgba(0, 0, 0, 0.1);
}

.offer-card:hover::before {
    opacity: 1;
}

.offer-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
    z-index: 1;
}

.offer-card-image {
    position: relative;
    overflow: hidden;
    height: 280px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.offer-card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, transparent 50%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.offer-card:hover .offer-card-image::before {
    opacity: 1;
}

.offer-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: brightness(1) saturate(1);
}

.offer-card:hover .offer-card-image img {
    transform: scale(1.15) rotate(1deg);
    filter: brightness(1.1) saturate(1.2);
}

.offer-card-body {
    padding: 2rem 1.5rem;
    position: relative;
}

.offer-card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(220, 53, 69, 0.2) 50%, transparent 100%);
}

.offer-card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
    position: relative;
}

.offer-card-title::after {
    content: '🔥';
    position: absolute;
    top: -5px;
    right: -25px;
    font-size: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
    animation: none;
}

[dir="rtl"] .offer-card-title::after {
    right: auto;
    left: -25px;
}

.offer-card:hover .offer-card-title::after {
    opacity: 1;
    animation: flame 1.5s ease-in-out infinite;
}

@keyframes flame {
    0%, 100% { transform: scale(1) rotate(-5deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

.offer-card-description {
    color: #495057;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-weight: 400;
}

.offer-card-actions {
    display: flex;
    gap: 1rem;
}

.offer-card-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.95rem;
    text-decoration: none;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.offer-card-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.offer-card-btn:hover::before {
    left: 100%;
}

.offer-card-btn-primary {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.offer-card-btn-primary:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 50%, #8b1a1a 100%);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.offer-card-btn-outline {
    background: transparent;
    color: #6c757d;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.offer-card-btn-outline:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border-color: #dc3545;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.15);
}

.offer-card-btn i {
    transition: transform 0.3s ease;
}

.offer-card-btn:hover i {
    transform: scale(1.1);
}

/* Professional Savings Badge */
.savings-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    animation: savingsPulse 2s ease-in-out infinite;
}

.savings-badge i {
    font-size: 0.9rem;
}

@keyframes savingsPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4); }
}

/* ==========================================================================
   Enhanced Arabic RTL Typography and Cultural Design
   ========================================================================== */

/* Arabic Typography Enhancements */
.special-offers-section,
.offers-section-header,
.offers-section-title,
.offers-section-subtitle,
.offers-banner-content,
.offer-card-body,
.no-offers-state {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Arabic Text Spacing and Line Height */
.offers-section-title {
    letter-spacing: -0.01em;
    word-spacing: 0.1em;
    line-height: 1.1;
}

.offers-section-subtitle {
    letter-spacing: 0.01em;
    word-spacing: 0.05em;
    line-height: 1.8;
}

.offers-banner-title {
    letter-spacing: -0.01em;
    word-spacing: 0.1em;
    line-height: 1.1;
}

.offers-banner-subtitle {
    letter-spacing: 0.01em;
    word-spacing: 0.05em;
    line-height: 1.6;
}

.offer-card-title {
    letter-spacing: 0.01em;
    word-spacing: 0.05em;
    line-height: 1.4;
}

.offer-card-description {
    letter-spacing: 0.02em;
    word-spacing: 0.05em;
    line-height: 1.8;
}

/* RTL-Specific Enhancements */
[dir="rtl"] .special-offers-section {
    text-align: right;
}

[dir="rtl"] .offers-section-header {
    text-align: center;
}

[dir="rtl"] .offers-banner-content {
    text-align: center;
}

[dir="rtl"] .offer-card-body {
    text-align: right;
}

[dir="rtl"] .offer-card-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .savings-badge {
    flex-direction: row-reverse;
}

/* Arabic Number Formatting */
.price-section,
.savings-badge,
.discount-badge {
    font-variant-numeric: tabular-nums;
    direction: ltr;
    unicode-bidi: embed;
}

/* Cultural Design Elements for Arabic Market */
.offers-section-title::before {
    content: '🏷️';
    margin-left: 0.5rem;
}

[dir="rtl"] .offers-section-title::before {
    margin-left: 0;
    margin-right: 0.5rem;
}

.offers-banner-title::before {
    content: '🎯';
    margin-left: 0.5rem;
}

[dir="rtl"] .offers-banner-title::before {
    margin-left: 0;
    margin-right: 0.5rem;
}

/* Enhanced Arabic Button Text */
.offer-card-btn,
.offers-banner-btn,
.no-offers-btn {
    font-weight: 600;
    letter-spacing: 0.01em;
    word-spacing: 0.05em;
}

/* Arabic-Specific Hover Effects */
[dir="rtl"] .offer-card-btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .offers-banner-btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Professional Arabic Color Scheme */
.special-offers-section {
    background:
        linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%),
        radial-gradient(circle at 25% 75%, rgba(220, 53, 69, 0.02) 0%, transparent 50%);
}

/* Arabic Market Cultural Colors */
.offers-section-title,
.offers-banner,
.discount-badge,
.offer-card::after {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 50%, #a71e2a 100%);
}

/* Enhanced Focus States for Arabic Users */
.offer-card-btn:focus,
.offers-banner-btn:focus,
.no-offers-btn:focus {
    outline: 3px solid rgba(220, 53, 69, 0.3);
    outline-offset: 2px;
}

/* Arabic Text Selection */
.special-offers-section ::selection {
    background: rgba(220, 53, 69, 0.2);
    color: #2c3e50;
}

.special-offers-section ::-moz-selection {
    background: rgba(220, 53, 69, 0.2);
    color: #2c3e50;
}

/* ==========================================================================
   Interactive Elements and Advanced Animations
   ========================================================================== */

/* Loading States */
.offer-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.offer-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid rgba(220, 53, 69, 0.3);
    border-top: 3px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Interactive Hover Animations */
.offer-card {
    cursor: pointer;
    will-change: transform, box-shadow;
}

.offer-card:hover {
    animation: cardFloat 0.6s ease-out forwards;
}

@keyframes cardFloat {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-8px) scale(1.02); }
    100% { transform: translateY(-12px) scale(1.03); }
}

/* Staggered Animation for Card Grid */
.offer-card:nth-child(1) { animation-delay: 0.1s; }
.offer-card:nth-child(2) { animation-delay: 0.2s; }
.offer-card:nth-child(3) { animation-delay: 0.3s; }
.offer-card:nth-child(4) { animation-delay: 0.4s; }
.offer-card:nth-child(5) { animation-delay: 0.5s; }
.offer-card:nth-child(6) { animation-delay: 0.6s; }

/* Page Load Animation */
.special-offers-section {
    animation: sectionFadeIn 1s ease-out;
}

@keyframes sectionFadeIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

.offers-section-header {
    animation: headerSlideIn 0.8s ease-out 0.2s both;
}

@keyframes headerSlideIn {
    0% { opacity: 0; transform: translateY(-20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.offers-banner {
    animation: bannerZoomIn 1s ease-out 0.4s both;
}

@keyframes bannerZoomIn {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

.offer-card {
    animation: cardSlideUp 0.6s ease-out both;
}

@keyframes cardSlideUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Interactive Button Effects */
.offer-card-btn {
    position: relative;
    overflow: hidden;
}

.offer-card-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.offer-card-btn:active::after {
    width: 300px;
    height: 300px;
}

/* Ripple Effect for Buttons */
.offer-card-btn {
    overflow: hidden;
}

.offer-card-btn:active {
    transform: scale(0.98);
}

/* Enhanced Discount Badge Animation */
.discount-badge {
    animation: badgeEntrance 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.6s both;
}

@keyframes badgeEntrance {
    0% {
        opacity: 0;
        transform: rotate(-5deg) scale(0) translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: rotate(-5deg) scale(1) translateY(0);
    }
}

.discount-badge:hover {
    animation: badgeBounce 0.6s ease-in-out;
}

@keyframes badgeBounce {
    0%, 100% { transform: rotate(-5deg) scale(1); }
    25% { transform: rotate(-8deg) scale(1.1); }
    75% { transform: rotate(-2deg) scale(1.05); }
}

/* Interactive Price Animation */
.price-section {
    transition: all 0.3s ease;
}

.offer-card:hover .price-section {
    transform: scale(1.05);
}

.savings-badge {
    transition: all 0.3s ease;
}

.offer-card:hover .savings-badge {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

/* Parallax Effect for Background Elements */
.special-offers-section::after {
    animation: floatParallax 15s ease-in-out infinite;
}

@keyframes floatParallax {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-20px, -15px) rotate(90deg); }
    50% { transform: translate(20px, -25px) rotate(180deg); }
    75% { transform: translate(-15px, 20px) rotate(270deg); }
}

/* Error State Animation */
.offer-card.error {
    animation: errorShake 0.5s ease-in-out;
    border-color: #dc3545;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success State Animation */
.offer-card.success {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08); }
    50% { transform: scale(1.02); box-shadow: 0 15px 50px rgba(40, 167, 69, 0.2); }
    100% { transform: scale(1); box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08); }
}

/* ==========================================================================
   Comprehensive Responsive Design and Accessibility
   ========================================================================== */

/* Large Desktop (1400px and up) */
@media (min-width: 1400px) {
    .special-offers-section {
        padding: 6rem 0;
    }

    .offers-section-title {
        font-size: 3.5rem;
    }

    .offers-section-subtitle {
        font-size: 1.4rem;
    }

    .offers-banner {
        min-height: 400px;
    }

    .offers-banner-title {
        font-size: 3.2rem;
    }

    .offers-banner-subtitle {
        font-size: 1.4rem;
    }

    .offer-card-image {
        height: 320px;
    }
}

/* Desktop (1200px to 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .offers-section-title {
        font-size: 3rem;
    }

    .offers-banner-title {
        font-size: 2.8rem;
    }

    .offer-card-image {
        height: 280px;
    }
}

/* Large Tablet (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .special-offers-section {
        padding: 4rem 0;
    }

    .offers-section-title {
        font-size: 2.5rem;
    }

    .offers-section-subtitle {
        font-size: 1.2rem;
    }

    .offers-banner {
        min-height: 320px;
    }

    .offers-banner-title {
        font-size: 2.4rem;
    }

    .offers-banner-content {
        padding: 3rem 2rem;
    }

    .offer-card-image {
        height: 260px;
    }

    .offer-card-body {
        padding: 1.5rem;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .special-offers-section {
        padding: 3.5rem 0;
    }

    .offers-section-header {
        margin-bottom: 3rem;
    }

    .offers-section-title {
        font-size: 2.2rem;
    }

    .offers-section-title::before {
        top: -15px;
        right: -35px;
        font-size: 1.8rem;
    }

    [dir="rtl"] .offers-section-title::before {
        right: auto;
        left: -35px;
    }

    .offers-section-subtitle {
        font-size: 1.1rem;
        max-width: 500px;
    }

    .offers-banner {
        min-height: 280px;
        margin-bottom: 3rem;
    }

    .offers-banner-title {
        font-size: 2rem;
    }

    .offers-banner-subtitle {
        font-size: 1.1rem;
    }

    .offers-banner-content {
        padding: 2.5rem 2rem;
    }

    .offers-banner-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .offer-card {
        margin-bottom: 2rem;
    }

    .offer-card-image {
        height: 240px;
    }

    .offer-card-body {
        padding: 1.5rem 1.25rem;
    }

    .offer-card-title {
        font-size: 1.2rem;
    }

    .offer-card-description {
        font-size: 0.95rem;
    }

    .offer-card-actions {
        gap: 0.75rem;
    }

    .offer-card-btn {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .special-offers-section {
        padding: 3rem 0;
    }

    .offers-section-header {
        margin-bottom: 2.5rem;
    }

    .offers-section-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .offers-section-title::before {
        top: -12px;
        right: -30px;
        font-size: 1.5rem;
    }

    [dir="rtl"] .offers-section-title::before {
        right: auto;
        left: -30px;
    }

    .offers-section-title::after {
        width: 100px;
        height: 5px;
    }

    .offers-section-subtitle {
        font-size: 1rem;
        max-width: 400px;
    }

    .offers-section-subtitle::before,
    .offers-section-subtitle::after {
        display: none;
    }

    .offers-banner {
        min-height: 250px;
        margin-bottom: 2.5rem;
        border-radius: 20px;
    }

    .offers-banner-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .offers-banner-title::before {
        top: -10px;
        right: -35px;
        font-size: 2rem;
    }

    [dir="rtl"] .offers-banner-title::before {
        right: auto;
        left: -35px;
    }

    .offers-banner-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .offers-banner-content {
        padding: 2rem 1.5rem;
    }

    .offers-banner-btn {
        padding: 0.9rem 1.8rem;
        font-size: 0.95rem;
        gap: 0.5rem;
    }

    .offer-card {
        margin-bottom: 1.5rem;
        border-radius: 20px;
    }

    .offer-card-image {
        height: 220px;
    }

    .offer-card-body {
        padding: 1.25rem 1rem;
    }

    .offer-card-title {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .offer-card-title::after {
        top: -3px;
        right: -20px;
        font-size: 0.9rem;
    }

    [dir="rtl"] .offer-card-title::after {
        right: auto;
        left: -20px;
    }

    .offer-card-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .offer-card-actions {
        gap: 0.5rem;
    }

    .offer-card-btn {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
        border-radius: 12px;
    }

    .savings-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .special-offers-section {
        padding: 2.5rem 0;
    }

    .offers-section-header {
        margin-bottom: 2rem;
    }

    .offers-section-title {
        font-size: 1.8rem;
        margin-bottom: 0.75rem;
    }

    .offers-section-title::before {
        display: none;
    }

    .offers-section-title::after {
        width: 80px;
        height: 4px;
    }

    .offers-section-subtitle {
        font-size: 0.95rem;
        max-width: 300px;
        line-height: 1.6;
    }

    .offers-banner {
        min-height: 220px;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .offers-banner-title {
        font-size: 1.6rem;
        margin-bottom: 0.75rem;
    }

    .offers-banner-title::before {
        display: none;
    }

    .offers-banner-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .offers-banner-content {
        padding: 1.5rem 1rem;
    }

    .offers-banner-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        border-radius: 50px;
    }

    .offer-card {
        margin-bottom: 1.25rem;
        border-radius: 15px;
    }

    .offer-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .offer-card-image {
        height: 200px;
    }

    .offer-card-body {
        padding: 1rem 0.75rem;
    }

    .offer-card-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .offer-card-title::after {
        display: none;
    }

    .offer-card-description {
        font-size: 0.85rem;
        margin-bottom: 1.25rem;
        line-height: 1.6;
    }

    .offer-card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .offer-card-btn {
        padding: 0.7rem 1rem;
        font-size: 0.8rem;
        border-radius: 10px;
    }

    .price-section .h4 {
        font-size: 1.1rem;
    }

    .savings-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .discount-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        top: 8px;
        right: 8px;
    }

    [dir="rtl"] .discount-badge {
        right: auto;
        left: 8px;
    }
}

/* ==========================================================================
   Accessibility Features
   ========================================================================== */

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .special-offers-section {
        background: #ffffff;
    }

    .offers-section-title {
        background: #000000;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .offer-card {
        border: 2px solid #000000;
    }

    .offer-card-btn-outline {
        border: 2px solid #000000;
        color: #000000;
    }

    .offer-card-btn-outline:hover {
        background: #000000;
        color: #ffffff;
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .special-offers-section::after {
        animation: none;
    }

    .offers-section-title::before,
    .offers-banner-title::before,
    .offer-card-title::after,
    .savings-badge,
    .discount-badge {
        animation: none;
    }
}

/* Focus Management */
.offer-card:focus-within {
    outline: 3px solid rgba(220, 53, 69, 0.5);
    outline-offset: 2px;
}

.offer-card-btn:focus,
.offers-banner-btn:focus {
    outline: 3px solid rgba(220, 53, 69, 0.5);
    outline-offset: 2px;
    z-index: 1;
}

/* Screen Reader Support */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Touch Target Sizes */
@media (pointer: coarse) {
    .offer-card-btn,
    .offers-banner-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .discount-badge {
        min-height: 32px;
        min-width: 32px;
    }
}

/* Print Styles */
@media print {
    .special-offers-section {
        background: white !important;
        color: black !important;
    }

    .special-offers-section::before,
    .special-offers-section::after {
        display: none !important;
    }

    .offer-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .offer-card-btn,
    .offers-banner-btn {
        display: none !important;
    }

    .offers-banner::before,
    .offers-banner::after {
        display: none !important;
    }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
    .special-offers-section {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
        color: #ffffff;
    }

    .offers-section-subtitle {
        color: #b0b0b0;
    }

    .offer-card {
        background: #2d2d2d;
        border-color: rgba(220, 53, 69, 0.3);
    }

    .offer-card-title {
        color: #ffffff;
    }

    .offer-card-description {
        color: #b0b0b0;
    }

    .offer-card-btn-outline {
        color: #b0b0b0;
        border-color: #404040;
    }

    .offer-card-btn-outline:hover {
        background: #404040;
        color: #ffffff;
    }
}

/* ==========================================================================
   Empty State for No Offers
   ========================================================================== */

.no-offers-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.no-offers-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.02) 0%, rgba(200, 35, 51, 0.02) 100%);
    pointer-events: none;
}

.no-offers-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.no-offers-icon i {
    font-size: 3rem;
    color: #dc3545;
    opacity: 0.7;
}

.no-offers-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 140px;
    height: 140px;
    border: 2px dashed rgba(220, 53, 69, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.no-offers-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.no-offers-message {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;
}

.no-offers-action {
    position: relative;
    z-index: 2;
}

.no-offers-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.no-offers-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Responsive Design for Empty State */
@media (max-width: 768px) {
    .no-offers-state {
        padding: 3rem 1.5rem;
        margin: 1.5rem 0;
    }

    .no-offers-icon {
        width: 100px;
        height: 100px;
        margin-bottom: 1.5rem;
    }

    .no-offers-icon i {
        font-size: 2.5rem;
    }

    .no-offers-icon::after {
        width: 120px;
        height: 120px;
    }

    .no-offers-title {
        font-size: 1.3rem;
    }

    .no-offers-message {
        font-size: 1rem;
    }

    .no-offers-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .no-offers-state {
        padding: 2rem 1rem;
    }

    .no-offers-icon {
        width: 80px;
        height: 80px;
    }

    .no-offers-icon i {
        font-size: 2rem;
    }

    .no-offers-icon::after {
        width: 100px;
        height: 100px;
    }

    .no-offers-title {
        font-size: 1.2rem;
    }

    .no-offers-message {
        font-size: 0.95rem;
    }
}

/* Feature Icons */
.feature-icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    transition: all 0.3s ease;
}

.feature-icon-wrapper:hover {
    transform: scale(1.1);
}

/* Section Headers */
.section-header {
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .carousel-image {
        height: 400px;
    }
    
    .carousel-caption-overlay h1 {
        font-size: 2rem;
    }
    
    .carousel-caption-overlay .lead {
        font-size: 1rem;
    }
    
    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }
    
    .carousel-control-prev {
        left: 10px;
    }
    
    .carousel-control-next {
        right: 10px;
    }
    
    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 16px;
        height: 16px;
    }
    
    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }
    
    .achievement-item h3 {
        font-size: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .btn {
        padding: 0.5rem 1.5rem;
    }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .hero-carousel-section {
        margin-bottom: 30px;
        border-radius: 0 0 15px 15px;
    }

    .carousel-image {
        height: 450px;
    }

    .carousel-title {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .carousel-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .carousel-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .carousel-image {
        height: 350px;
    }

    .carousel-caption-overlay {
        padding: 20px 0;
    }

    .carousel-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .carousel-subtitle {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .carousel-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        margin-bottom: 10px;
        display: block;
        text-align: center;
    }

    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }

    .section-header h2 {
        font-size: 1.75rem;
    }

    .achievement-item {
        padding: 1rem;
    }
}

/* Enhanced Typography for Carousel */
.carousel-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #fff;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.carousel-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.95);
    text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-weight: 400;
}

.carousel-actions {
    margin-top: 2rem;
}

.carousel-btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.carousel-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.3);
}

/* Enhanced Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.animate-slide-up {
    animation: slideUp 1s ease-out;
}

.animate-slide-up-delay {
    animation: slideUp 1s ease-out 0.3s both;
}

.animate-fade-in-delay {
    animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Professional Enhanced Components - Version 3.0
   ========================================================================== */

/* Enhanced Offers Banner */
.offers-banner {
    position: relative;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.offers-banner:hover {
    transform: scale(1.02);
}

.offers-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.6) 100%);
    z-index: 1;
}

.offers-banner .banner-content {
    position: relative;
    z-index: 2;
    padding: 3rem;
    text-align: center;
    color: white;
}

.offers-banner .banner-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.offers-banner .banner-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.offers-banner .banner-btn {
    background: linear-gradient(135deg, #ffc107 0%, #ff9500 100%);
    color: #2c3e50;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.offers-banner .banner-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
    color: #2c3e50;
}

/* Enhanced Feature Icons */
.feature-icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1.5rem;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.feature-icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-icon-wrapper:hover::before {
    opacity: 1;
}

.feature-icon-wrapper:hover {
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.feature-icon-wrapper i {
    font-size: 2rem;
    z-index: 2;
    position: relative;
}

/* Enhanced CTA Section */
.cta-section {
    position: relative;
    padding: 5rem 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-btn {
    padding: 1.2rem 3rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.cta-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.cta-btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

/* ==========================================================================
   Professional Section Spacing & Layout
   ========================================================================== */

.homepage-section {
    position: relative;
    padding: 5rem 0;
    margin: 0;
    overflow: hidden;
}

.homepage-section:not(:last-child) {
    margin-bottom: 0;
}

.homepage-section + .homepage-section {
    border-top: 1px solid rgba(0,0,0,0.05);
}

/* Section Background Variations */
.homepage-section.bg-white {
    background-color: #ffffff;
}

.homepage-section.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.homepage-section.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.homepage-section.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.homepage-section.bg-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

/* Professional Section Dividers */
.section-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #e9ecef 20%, #dee2e6 50%, #e9ecef 80%, transparent 100%);
    margin: 0;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 6px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
}

.section-divider.divider-gradient {
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 20%, rgba(118, 75, 162, 0.5) 50%, rgba(102, 126, 234, 0.3) 80%, transparent 100%);
    height: 3px;
}

.section-divider.divider-dots {
    background: none;
    text-align: center;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-divider.divider-dots::before {
    content: '◆ ◆ ◆';
    color: #667eea;
    font-size: 1.2rem;
    letter-spacing: 1rem;
    width: auto;
    height: auto;
    background: none;
    border-radius: 0;
    top: auto;
    left: auto;
    transform: none;
}

.section-divider.divider-wave {
    background: none;
    height: 40px;
    position: relative;
}

.section-divider.divider-wave::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%);
    transform: translateY(-50%);
    width: 100%;
    border-radius: 0;
}

.section-divider.divider-wave::after {
    content: '〜';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #667eea;
    font-size: 2rem;
    background: white;
    padding: 0 1rem;
}

/* Enhanced Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.section-header.header-left {
    text-align: right;
}

.section-header.header-right {
    text-align: left;
}

.section-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    line-height: 1.2;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-header.header-left .section-title::after {
    left: 0;
    transform: none;
}

.section-header.header-right .section-title::after {
    right: 0;
    left: auto;
    transform: none;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.8;
    font-weight: 400;
}

.homepage-section.bg-gradient-primary .section-title,
.homepage-section.bg-gradient-success .section-title,
.homepage-section.bg-dark .section-title {
    color: white;
}

.homepage-section.bg-gradient-primary .section-subtitle,
.homepage-section.bg-gradient-success .section-subtitle,
.homepage-section.bg-dark .section-subtitle {
    color: rgba(255, 255, 255, 0.85);
}

.homepage-section.bg-gradient-primary .section-title::after,
.homepage-section.bg-gradient-success .section-title::after,
.homepage-section.bg-dark .section-title::after {
    background: rgba(255, 255, 255, 0.4);
}

/* ==========================================================================
   Professional Responsive Design - Mobile First
   ========================================================================== */

@media (max-width: 1200px) {
    .homepage-section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 992px) {
    .homepage-section {
        padding: 3.5rem 0;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .feature-icon-wrapper {
        width: 70px;
        height: 70px;
        margin-left: 1rem;
        margin-bottom: 1rem;
    }

    .feature-icon-wrapper i {
        font-size: 1.8rem;
    }

    .offers-banner .banner-title {
        font-size: 2.5rem;
    }

    .offers-banner .banner-content {
        padding: 2.5rem;
    }
}

@media (max-width: 768px) {
    .homepage-section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
        max-width: 100%;
    }

    .section-header {
        margin-bottom: 2.5rem;
    }

    .product-card .card-img-top {
        height: 220px;
    }

    .feature-icon-wrapper {
        width: 60px;
        height: 60px;
        margin: 0 auto 1rem;
    }

    .feature-icon-wrapper i {
        font-size: 1.5rem;
    }

    .offers-banner .banner-title {
        font-size: 2rem;
    }

    .offers-banner .banner-subtitle {
        font-size: 1.1rem;
    }

    .offers-banner .banner-content {
        padding: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .cta-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .newsletter-form .form-control {
        margin-bottom: 1rem;
    }

    .achievement-item {
        margin-bottom: 1.5rem;
    }

    .achievement-item h3 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .homepage-section {
        padding: 2.5rem 0;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-title::after {
        width: 60px;
        height: 3px;
    }

    .product-card .card-img-top {
        height: 200px;
    }

    .product-card .card-body {
        padding: 1.25rem;
    }

    .offers-banner .banner-title {
        font-size: 1.8rem;
    }

    .offers-banner .banner-subtitle {
        font-size: 1rem;
    }

    .offers-banner .banner-content {
        padding: 1.5rem;
    }

    .newsletter-section .container {
        padding: 3rem 0;
    }

    .achievement-item {
        padding: 1.5rem;
    }

    .achievement-item h3 {
        font-size: 1.8rem;
    }

    .section-divider.divider-dots::before {
        letter-spacing: 0.5rem;
        font-size: 1rem;
    }
}

/* ==========================================================================
   Professional Animation & Interaction Enhancements
   ========================================================================== */

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.animate-on-scroll.delay-1 {
    transition-delay: 0.1s;
}

.animate-on-scroll.delay-2 {
    transition-delay: 0.2s;
}

.animate-on-scroll.delay-3 {
    transition-delay: 0.3s;
}

/* Hover Effects for Interactive Elements */
.interactive-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.interactive-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

/* Professional Loading States */
.section-loading {
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Print Styles */
@media print {
    .homepage-section {
        padding: 1rem 0;
        break-inside: avoid;
    }

    .section-divider,
    .cta-section,
    .newsletter-section {
        display: none !important;
    }

    .product-card,
    .influencer-card,
    .review-card,
    .achievement-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
        margin-bottom: 1rem !important;
    }

    .section-title {
        color: #000 !important;
        font-size: 1.5rem !important;
    }

    .section-subtitle {
        color: #666 !important;
        font-size: 1rem !important;
    }
}

/* ==========================================================================
   Professional Category Cards
   ========================================================================== */

.category-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    position: relative;
    height: 300px;
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.category-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s ease;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.category-count {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

/* ==========================================================================
   Professional Promotional Banners
   ========================================================================== */

.promo-banner {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 250px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.promo-banner:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0,0,0,0.2);
}

.promo-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s ease;
}

.promo-banner:hover .promo-banner-image {
    transform: scale(1.05);
}

.promo-banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    padding: 2rem;
}

.promo-banner-title {
    font-size: 1.8rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.promo-banner-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
}

.promo-banner .btn {
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.promo-banner .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* ==========================================================================
   Enhanced Responsive Design for New Sections
   ========================================================================== */

@media (max-width: 768px) {
    .category-card {
        height: 250px;
    }

    .category-title {
        font-size: 1.3rem;
    }

    .category-content {
        padding: 1.5rem;
    }

    .promo-banner {
        height: 200px;
        margin-bottom: 1rem;
    }

    .promo-banner-title {
        font-size: 1.5rem;
    }

    .promo-banner-subtitle {
        font-size: 1rem;
    }

    .promo-banner-content {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .category-card {
        height: 220px;
    }

    .category-title {
        font-size: 1.2rem;
    }

    .promo-banner {
        height: 180px;
    }

    .promo-banner-title {
        font-size: 1.3rem;
    }

    .promo-banner-content {
        padding: 1rem;
    }
}

/* ==========================================================================
   Professional Social Media Integration
   ========================================================================== */

.social-media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.social-media-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    border-radius: 20px;
    color: white;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    min-height: 80px;
}

.social-media-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.social-media-item:hover::before {
    left: 100%;
}

.social-media-item:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0,0,0,0.25);
    color: white;
    text-decoration: none;
}

.social-media-item i {
    font-size: 2rem;
    margin-left: 1rem;
}

.social-media-item span {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Social Media Responsive Design */
@media (max-width: 768px) {
    .social-media-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .social-media-item {
        padding: 1rem;
        min-height: 70px;
    }

    .social-media-item i {
        font-size: 1.5rem;
        margin-left: 0.5rem;
    }

    .social-media-item span {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .social-media-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .social-media-item {
        padding: 1rem;
        min-height: 60px;
    }
}

/* ==========================================================================
   Floating WhatsApp Button
   ========================================================================== */

.floating-whatsapp {
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: 1000;
    animation: float 3s ease-in-out infinite;
}

[dir="rtl"] .floating-whatsapp {
    left: auto;
    right: 30px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.whatsapp-btn {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-weight: 600;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.whatsapp-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.whatsapp-btn:hover::before {
    left: 100%;
}

.whatsapp-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

.whatsapp-btn i {
    font-size: 1.5rem;
    margin-left: 10px;
}

[dir="rtl"] .whatsapp-btn i {
    margin-left: 0;
    margin-right: 10px;
}

.whatsapp-text {
    font-size: 0.95rem;
    white-space: nowrap;
}

/* Mobile WhatsApp Button */
@media (max-width: 768px) {
    .floating-whatsapp {
        bottom: 20px;
        left: 20px;
    }

    [dir="rtl"] .floating-whatsapp {
        left: auto;
        right: 20px;
    }

    .whatsapp-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .whatsapp-btn i {
        font-size: 1.3rem;
        margin-left: 8px;
    }

    [dir="rtl"] .whatsapp-btn i {
        margin-left: 0;
        margin-right: 8px;
    }

    .whatsapp-text {
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .whatsapp-btn {
        padding: 10px 14px;
    }

    .whatsapp-text {
        display: none;
    }

    .whatsapp-btn i {
        margin: 0;
        font-size: 1.4rem;
    }
}

/* ==========================================================================
   Enhanced Toast Notification System
   ========================================================================== */

.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 9999;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid;
}

[dir="rtl"] .toast-notification {
    right: auto;
    left: 20px;
    border-left: none;
    border-right: 4px solid;
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-success {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.toast-error {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.toast-info {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-grow: 1;
}

.toast-content i {
    font-size: 1.2rem;
}

.toast-success .toast-content i {
    color: #28a745;
}

.toast-error .toast-content i {
    color: #dc3545;
}

.toast-info .toast-content i {
    color: #17a2b8;
}

.toast-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: rgba(0,0,0,0.1);
    color: #333;
}

@media (max-width: 768px) {
    .toast-notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: calc(100vw - 20px);
    }

    [dir="rtl"] .toast-notification {
        right: 10px;
        left: 10px;
    }
}

/* ==========================================================================
   Enhanced Testimonial Carousel
   ========================================================================== */

.testimonial-carousel-container {
    position: relative;
    margin-top: 2rem;
}

.testimonial-carousel {
    overflow: hidden;
    border-radius: var(--border-radius-lg);
}

.testimonial-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    gap: 1.5rem;
}

.testimonial-slide {
    min-width: 100%;
    flex-shrink: 0;
}

@media (min-width: 768px) {
    .testimonial-slide {
        min-width: calc(50% - 0.75rem);
    }
}

@media (min-width: 992px) {
    .testimonial-slide {
        min-width: calc(33.333% - 1rem);
    }
}

.carousel-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 2;
}

.carousel-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    pointer-events: all;
    color: #333;
}

.carousel-btn:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
    color: #667eea;
}

.carousel-btn i {
    font-size: 1.2rem;
}

.carousel-prev {
    left: -25px;
}

.carousel-next {
    right: -25px;
}

[dir="rtl"] .carousel-prev {
    left: auto;
    right: -25px;
}

[dir="rtl"] .carousel-next {
    right: auto;
    left: -25px;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active,
.carousel-indicator:hover {
    background: var(--primary-gradient);
    transform: scale(1.2);
}

/* Auto-play animation */
.testimonial-carousel.auto-play .testimonial-track {
    animation: testimonialAutoPlay 20s infinite linear;
}

@keyframes testimonialAutoPlay {
    0%, 20% { transform: translateX(0); }
    25%, 45% { transform: translateX(-100%); }
    50%, 70% { transform: translateX(-200%); }
    75%, 95% { transform: translateX(-300%); }
    100% { transform: translateX(0); }
}

/* Mobile carousel adjustments */
@media (max-width: 768px) {
    .carousel-btn {
        width: 40px;
        height: 40px;
    }

    .carousel-prev {
        left: -20px;
    }

    .carousel-next {
        right: -20px;
    }

    [dir="rtl"] .carousel-prev {
        right: -20px;
    }

    [dir="rtl"] .carousel-next {
        left: -20px;
    }
}

/* ==========================================================================
   Enhanced RTL Support & Arabic Typography
   ========================================================================== */

[dir="rtl"] .homepage-section {
    text-align: right;
}

[dir="rtl"] .section-header.header-left {
    text-align: right;
}

[dir="rtl"] .section-header.header-right {
    text-align: left;
}

[dir="rtl"] .feature-icon-wrapper {
    margin-right: 1.5rem;
    margin-left: 0;
}

[dir="rtl"] .social-media-item i {
    margin-right: 1rem;
    margin-left: 0;
}

[dir="rtl"] .product-actions {
    direction: rtl;
}

[dir="rtl"] .cta-buttons {
    direction: rtl;
}

/* Arabic Font Optimization */
.homepage-section h1,
.homepage-section h2,
.homepage-section h3,
.homepage-section h4,
.homepage-section h5,
.homepage-section h6 {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    line-height: 1.4;
}

.homepage-section p,
.homepage-section span,
.homepage-section div {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
}

/* ==========================================================================
   Professional Breadcrumb Navigation
   ========================================================================== */

.breadcrumb-nav {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
}

.breadcrumb {
    background: none;
    margin-bottom: 0;
    padding: 0;
    font-size: 0.9rem;
}

.breadcrumb-item {
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    color: #6c757d;
    margin: 0 0.5rem;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #764ba2;
    text-decoration: underline;
}

.breadcrumb-item i {
    font-size: 0.9rem;
}

/* ==========================================================================
   Currency Converter Widget
   ========================================================================== */

.currency-widget {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 0;
    font-size: 0.9rem;
}

.currency-converter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.currency-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
    font-weight: 500;
}

.currency-info i {
    color: #667eea;
    font-size: 1rem;
}

.region-selector {
    min-width: 150px;
}

.region-selector .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    transition: all 0.3s ease;
}

.region-selector .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

@media (max-width: 768px) {
    .currency-converter {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .region-selector {
        min-width: 120px;
    }
}

/* ==========================================================================
   Advanced Loading Animations & Visual Enhancements
   ========================================================================== */

/* Professional Loading Skeleton */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
    border-radius: 8px;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Smooth Scroll Behavior */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

/* Professional Page Loading Animation */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.page-loader.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Section Entrance Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.animate-on-scroll.delay-1 { transition-delay: 0.1s; }
.animate-on-scroll.delay-2 { transition-delay: 0.2s; }
.animate-on-scroll.delay-3 { transition-delay: 0.3s; }
.animate-on-scroll.delay-4 { transition-delay: 0.4s; }

/* Advanced Hover Effects for Interactive Elements */
.interactive-element {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.interactive-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
    z-index: 1;
}

.interactive-element:hover::before {
    left: 100%;
}

/* Enhanced Button System with Professional Hover Effects */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
    z-index: 1;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-enhanced:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Professional Color System Enhancement */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    --dark-gradient: linear-gradient(135deg, #343a40 0%, #495057 100%);

    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-xl: 0 15px 40px rgba(0,0,0,0.2);

    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 30px;
}

/* Enhanced Visual Hierarchy with Depth */
.depth-1 { box-shadow: var(--shadow-sm); }
.depth-2 { box-shadow: var(--shadow-md); }
.depth-3 { box-shadow: var(--shadow-lg); }
.depth-4 { box-shadow: var(--shadow-xl); }

.depth-1:hover { box-shadow: var(--shadow-md); }
.depth-2:hover { box-shadow: var(--shadow-lg); }
.depth-3:hover { box-shadow: var(--shadow-xl); }

/* Professional Gradient Backgrounds */
.bg-gradient-primary { background: var(--primary-gradient); }
.bg-gradient-success { background: var(--success-gradient); }
.bg-gradient-warning { background: var(--warning-gradient); }
.bg-gradient-danger { background: var(--danger-gradient); }
.bg-gradient-info { background: var(--info-gradient); }
.bg-gradient-dark { background: var(--dark-gradient); }

/* ==========================================================================
   Enhanced Mobile-First Responsive Design
   ========================================================================== */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 320px) {
    .homepage-section {
        padding: 2rem 0;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }

    .product-card .card-img-top {
        height: 180px;
    }

    .category-card {
        height: 200px;
    }

    .promo-banner {
        height: 160px;
    }

    .achievement-item {
        padding: 1rem;
    }

    .achievement-item h3 {
        font-size: 1.5rem;
    }
}

/* Small Devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .homepage-section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .product-card .card-img-top {
        height: 220px;
    }

    .category-card {
        height: 240px;
    }

    .promo-banner {
        height: 200px;
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .homepage-section {
        padding: 3.5rem 0;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .product-card .card-img-top {
        height: 250px;
    }

    .category-card {
        height: 280px;
    }

    .promo-banner {
        height: 230px;
    }
}

/* Large Devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {
    .homepage-section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .product-card .card-img-top {
        height: 280px;
    }

    .category-card {
        height: 300px;
    }

    .promo-banner {
        height: 250px;
    }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .homepage-section {
        padding: 5rem 0;
    }

    .section-title {
        font-size: 2.8rem;
    }

    .container {
        max-width: 1200px;
    }
}

/* ==========================================================================
   Performance Optimizations
   ========================================================================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .homepage-section,
    .product-card,
    .category-card,
    .promo-banner,
    .social-media-item,
    .animate-on-scroll {
        transition: none !important;
        animation: none !important;
    }

    .product-card:hover,
    .category-card:hover,
    .promo-banner:hover,
    .social-media-item:hover {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .homepage-section {
        border: 2px solid;
    }

    .section-divider {
        background: currentColor;
        height: 3px;
    }

    .product-card,
    .category-card,
    .promo-banner {
        border: 2px solid;
    }
}

/* ==========================================================================
   Arabic E-commerce Specific Features
   ========================================================================== */

/* Professional Arabic Typography Hierarchy */
.arabic-title {
    font-family: 'Cairo', 'Amiri', serif;
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.5px;
}

.arabic-subtitle {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    line-height: 1.6;
    color: #6c757d;
}

.arabic-body {
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
    line-height: 1.8;
    text-align: justify;
}

/* Arabic Date and Time Display */
.arabic-date {
    direction: rtl;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    color: #495057;
}

.arabic-date .hijri-date {
    color: #667eea;
    font-weight: 600;
}

.arabic-date .gregorian-date {
    color: #6c757d;
    font-size: 0.9em;
}

/* RTL-Specific Animations */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

[dir="rtl"] .slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
}

[dir="rtl"] .slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
}

/* Arabic Number Formatting */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
    direction: ltr;
    unicode-bidi: embed;
}

/* Enhanced RTL Support for Interactive Elements */
[dir="rtl"] .btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Regional Pricing Display */
.regional-pricing {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.regional-pricing .region-name {
    font-weight: 600;
    color: #1976d2;
}

.regional-pricing .delivery-cost {
    color: #388e3c;
    font-weight: 500;
}

/* Arabic Offer Countdown */
.arabic-countdown {
    direction: rtl;
    font-family: 'Cairo', sans-serif;
    text-align: center;
}

.countdown-item {
    display: inline-block;
    margin: 0 0.5rem;
    padding: 0.5rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 0.5rem;
    min-width: 60px;
}

.countdown-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.countdown-label {
    display: block;
    font-size: 0.8rem;
    opacity: 0.9;
    margin-top: 0.25rem;
}

/* ==========================================================================
   Advanced Professional Enhancements
   ========================================================================== */

/* Enhanced Product Card Animations */
.product-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
    will-change: transform, box-shadow;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    z-index: 1;
}

.product-card:hover::before {
    opacity: 1;
}

/* Enhanced Image Container with Overlay Effects */
.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.product-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-image-container::after {
    opacity: 1;
}

.product-image-container img {
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card:hover .product-image-container img {
    transform: scale(1.1);
}

/* Enhanced Quick Actions with Better Positioning */
.quick-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10;
}

.product-card:hover .quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.95);
    color: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* Enhanced Professional Discount Badge */
.discount-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 0.6rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    z-index: 5;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    animation: discountPulse 3s ease-in-out infinite;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transform: rotate(-5deg);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.discount-badge:hover {
    transform: rotate(0deg) scale(1.1);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
}

.discount-badge::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border-radius: 25px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.discount-badge:hover::before {
    opacity: 1;
}

@keyframes discountPulse {
    0%, 100% {
        transform: rotate(-5deg) scale(1);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    }
    50% {
        transform: rotate(-5deg) scale(1.05);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
    }
}

/* RTL Support for Discount Badge */
[dir="rtl"] .discount-badge {
    right: auto;
    left: 12px;
    transform: rotate(5deg);
}

[dir="rtl"] .discount-badge:hover {
    transform: rotate(0deg) scale(1.1);
}

@media (max-width: 768px) {
    .discount-badge {
        top: 8px;
        right: 8px;
        padding: 0.4rem 0.7rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    [dir="rtl"] .discount-badge {
        left: 8px;
        right: auto;
    }
}

@media (max-width: 576px) {
    .discount-badge {
        top: 6px;
        right: 6px;
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
        border-radius: 15px;
    }

    [dir="rtl"] .discount-badge {
        left: 6px;
        right: auto;
    }
}

/* Enhanced Card Body */
.product-card .card-body {
    position: relative;
    z-index: 2;
    padding: 1.5rem;
    background: white;
    border-radius: 0 0 15px 15px;
}

.product-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-card .card-text {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #6c757d;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced Price Display */
.product-price {
    margin: 1rem 0;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #28a745;
    display: block;
}

.original-price {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-top: 0.25rem;
}

/* Enhanced Product Actions */
.product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: auto;
}

.btn-product {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.btn-product::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.btn-product:hover::before {
    left: 100%;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* ==========================================================================
   Enhanced Section Headers
   ========================================================================== */

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    right: -10px;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-header:hover .section-title::before {
    opacity: 1;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Enhanced Hero Carousel */
.hero-carousel {
    position: relative;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.carousel-item {
    position: relative;
    height: 500px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
    z-index: 1;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.carousel-item:hover img {
    transform: scale(1.05);
}

.carousel-caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 2;
    width: 90%;
    max-width: 600px;
}

.carousel-caption h2 {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out;
}

.carousel-caption p {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out 0.2s both;
}

.carousel-caption .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    background: rgba(255,255,255,0.2);
    border: 2px solid white;
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.carousel-caption .btn:hover {
    background: white;
    color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255,255,255,0.3);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.carousel-control-prev {
    left: 20px;
}

.carousel-control-next {
    right: 20px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-50%) scale(1.1);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 24px;
    height: 24px;
}

/* Enhanced Carousel Indicators */
.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.carousel-indicators .active {
    background: white;
    transform: scale(1.2);
}

/* ==========================================================================
   Enhanced Category Cards
   ========================================================================== */

.category-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    height: 300px;
    cursor: pointer;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 2;
}

.category-card:hover::before {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.2);
}

.category-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-card .card-body {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.8) 100%);
    color: white;
    padding: 2rem;
    z-index: 3;
    transform: translateY(20px);
    transition: transform 0.4s ease;
}

.category-card:hover .card-body {
    transform: translateY(0);
}

.category-card .card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.category-card .card-text {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

/* Enhanced Testimonial Cards */
.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform 0.4s ease;
}

.testimonial-card:hover::before {
    transform: scaleY(1);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    border: 4px solid #f8f9fa;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.testimonial-card:hover .testimonial-avatar {
    transform: scale(1.1);
}

.testimonial-text {
    font-style: italic;
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    position: relative;
}

.testimonial-text::before {
    content: '"';
    font-size: 4rem;
    color: #e9ecef;
    position: absolute;
    top: -1rem;
    left: -0.5rem;
    font-family: serif;
}

.testimonial-author {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.testimonial-role {
    color: #6c757d;
    font-size: 0.9rem;
}

.testimonial-rating {
    margin-top: 1rem;
    color: #ffc107;
}

/* Enhanced Social Media Cards */
.social-media-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.social-media-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-gradient));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-media-item:hover::before {
    opacity: 0.1;
}

.social-media-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.social-media-item i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.social-media-item:hover i {
    transform: scale(1.2);
    color: #667eea;
}

.social-media-item h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.social-media-item p {
    color: #6c757d;
    font-size: 0.9rem;
    position: relative;
    z-index: 2;
}

/* ==========================================================================
   Advanced Interactive Elements
   ========================================================================== */

/* Enhanced Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.newsletter-form {
    position: relative;
    z-index: 2;
}

.newsletter-form .form-control {
    border-radius: 50px;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.newsletter-form .form-control:focus {
    border-color: white;
    background: rgba(255,255,255,0.2);
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.newsletter-form .btn {
    border-radius: 50px;
    padding: 1rem 2rem;
    background: white;
    color: #667eea;
    border: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.newsletter-form .btn:hover {
    background: rgba(255,255,255,0.9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,255,255,0.3);
}

/* Enhanced Achievement Items */
.achievement-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.achievement-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.achievement-item:hover::before {
    transform: scaleX(1);
}

.achievement-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.achievement-item i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.achievement-item:hover i {
    transform: scale(1.2);
    color: #764ba2;
}

.achievement-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    counter-reset: achievement;
}

.achievement-item p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

/* Enhanced Promotional Banners */
.promo-banner {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 250px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.2) 100%);
    z-index: 2;
    transition: opacity 0.3s ease;
}

.promo-banner:hover::before {
    opacity: 0.8;
}

.promo-banner:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.promo-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.promo-banner:hover img {
    transform: scale(1.1);
}

.promo-banner .banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 3;
    width: 90%;
}

.promo-banner .banner-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.promo-banner .banner-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Enhanced Loading States */
.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    width: 70%;
}

.skeleton-image {
    height: 200px;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Enhanced Scroll Indicators */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(102, 126, 234, 0.2);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.1s ease;
}

/* Enhanced Back to Top Button */
.back-to-top, .back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.9;
    visibility: visible;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    font-size: 1.2rem;
    font-weight: bold;
}

.back-to-top.show, .back-to-top-btn.show {
    opacity: 0.9;
    visibility: visible;
}

.back-to-top:hover, .back-to-top-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    opacity: 1;
}

[dir="rtl"] .back-to-top, [dir="rtl"] .back-to-top-btn {
    right: auto;
    left: 30px;
}
